# Script per installare l'add-in di Outlook
# Eseguire come Amministratore

param(
    [string]$AddInPath = ".\OutlookAddIn1_1_0_0_2\OutlookAddIn1.vsto"
)

Write-Host "=== Installazione Add-in Outlook ===" -ForegroundColor Green

# Verifica se il file esiste
if (-not (Test-Path $AddInPath)) {
    Write-Error "File add-in non trovato: $AddInPath"
    exit 1
}

# Ottieni il percorso completo
$FullPath = (Resolve-Path $AddInPath).Path
Write-Host "Percorso add-in: $FullPath" -ForegroundColor Yellow

# Verifica prerequisiti
Write-Host "Verifica prerequisiti..." -ForegroundColor Yellow

# Controlla se .NET Framework 4.7.2 o superiore è installato
$dotNetVersion = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
if ($dotNetVersion -and $dotNetVersion.Release -ge 461808) {
    Write-Host "✓ .NET Framework 4.7.2+ installato" -ForegroundColor Green
} else {
    Write-Warning ".NET Framework 4.7.2 o superiore richiesto"
    Write-Host "Scarica da: https://dotnet.microsoft.com/download/dotnet-framework"
}

# Controlla se VSTO Runtime è installato
$vstoRuntime = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Visual Studio*Tools*Office*Runtime*" }
if ($vstoRuntime) {
    Write-Host "✓ VSTO Runtime installato: $($vstoRuntime.Name)" -ForegroundColor Green
} else {
    Write-Warning "VSTO Runtime non trovato"
    Write-Host "Scarica da: https://www.microsoft.com/download/details.aspx?id=48217"
}

# Controlla se Outlook è installato
$outlook = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Office*" -or $_.Name -like "*Outlook*" }
if ($outlook) {
    Write-Host "✓ Microsoft Office/Outlook installato" -ForegroundColor Green
} else {
    Write-Warning "Microsoft Office/Outlook non trovato"
}

# Installa l'add-in
Write-Host "Installazione add-in..." -ForegroundColor Yellow

try {
    # Metodo 1: Usa VSTOInstaller
    $vstoInstaller = "${env:ProgramFiles}\Common Files\Microsoft Shared\VSTO\10.0\VSTOInstaller.exe"
    if (-not (Test-Path $vstoInstaller)) {
        $vstoInstaller = "${env:ProgramFiles(x86)}\Common Files\Microsoft Shared\VSTO\10.0\VSTOInstaller.exe"
    }
    
    if (Test-Path $vstoInstaller) {
        Write-Host "Usando VSTOInstaller..." -ForegroundColor Yellow
        & "$vstoInstaller" /Install "$FullPath"
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Add-in installato con successo!" -ForegroundColor Green
        } else {
            Write-Error "Errore durante l'installazione (codice: $LASTEXITCODE)"
        }
    } else {
        # Metodo 2: Registrazione manuale nel registry
        Write-Host "VSTOInstaller non trovato, registrazione manuale..." -ForegroundColor Yellow
        
        $regPath = "HKCU:\Software\Microsoft\Office\Outlook\Addins\OutlookAddIn1"
        
        if (-not (Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        Set-ItemProperty -Path $regPath -Name "Description" -Value "OutlookAddIn1"
        Set-ItemProperty -Path $regPath -Name "FriendlyName" -Value "OutlookAddIn1"
        Set-ItemProperty -Path $regPath -Name "LoadBehavior" -Value 3 -Type DWord
        Set-ItemProperty -Path $regPath -Name "Manifest" -Value $FullPath
        
        Write-Host "✓ Add-in registrato nel registry" -ForegroundColor Green
    }
    
    Write-Host "=== Installazione completata ===" -ForegroundColor Green
    Write-Host "Riavvia Outlook per vedere l'add-in" -ForegroundColor Yellow
    
} catch {
    Write-Error "Errore durante l'installazione: $($_.Exception.Message)"
    Write-Host "Prova a eseguire lo script come Amministratore" -ForegroundColor Yellow
}

# Istruzioni aggiuntive
Write-Host "`n=== Risoluzione problemi ===" -ForegroundColor Cyan
Write-Host "Se l'add-in non appare in Outlook:"
Write-Host "1. Vai in File > Opzioni > Componenti aggiuntivi"
Write-Host "2. Seleziona 'Componenti aggiuntivi COM' e clicca 'Vai'"
Write-Host "3. Verifica che 'OutlookAddIn1' sia presente e abilitato"
Write-Host "4. Se non è presente, clicca 'Aggiungi' e seleziona il file .vsto"
