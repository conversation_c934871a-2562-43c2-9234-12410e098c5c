===============================================
INSTALLAZIONE MANUALE ADD-IN OUTLOOK
===============================================

Il tuo add-in ha problemi con le firme digitali VSTO. 
Ecco come installarlo manualmente:

METODO 1: REGISTRAZIONE COM (RACCOMANDATO)
==========================================

1. Apri il Prompt dei comandi come AMMINISTRATORE
   - Premi Win+R, digita "cmd"
   - Tieni premuto Ctrl+Shift e premi Enter
   - Clicca "Sì" quando richiesto

2. Naviga nella cartella dell'add-in:
   cd "C:\Users\<USER>\Desktop\Nuova cartella (3)\Application Files\OutlookAddIn1_1_0_0_2"

3. Registra la DLL:
   "%ProgramFiles(x86)%\Microsoft.NET\Framework\v4.0.30319\RegAsm.exe" OutlookAddIn1.dll /codebase

   OPPURE se il comando sopra non funziona:
   "%ProgramFiles(x86)%\Microsoft.NET\Framework64\v4.0.30319\RegAsm.exe" OutlookAddIn1.dll /codebase

4. Apri l'Editor del Registro (regedit) come AMMINISTRATORE

5. Vai a: HKEY_CURRENT_USER\Software\Microsoft\Office\Outlook\Addins

6. Crea una nuova chiave chiamata: OutlookAddIn1.ThisAddIn

7. Nella chiave creata, aggiungi questi valori:
   - Nome: Description
     Tipo: Stringa
     Valore: OutlookAddIn1 - Add-in personalizzato

   - Nome: FriendlyName
     Tipo: Stringa
     Valore: OutlookAddIn1

   - Nome: LoadBehavior
     Tipo: DWORD (32-bit)
     Valore: 3

8. Chiudi l'Editor del Registro

9. CHIUDI Outlook completamente se è aperto

10. RIAVVIA Outlook


METODO 2: USANDO IL FILE .REG
=============================

1. Fai doppio clic sul file "Install-AddIn.reg"
2. Clicca "Sì" per confermare l'aggiunta al registro
3. Chiudi e riavvia Outlook


VERIFICA INSTALLAZIONE
======================

1. Apri Outlook
2. Vai in File > Opzioni > Componenti aggiuntivi
3. Nel menu a discesa "Gestisci", seleziona "Componenti aggiuntivi COM"
4. Clicca "Vai..."
5. Verifica che "OutlookAddIn1" sia presente nell'elenco
6. Se presente ma non abilitato, spunta la casella accanto ad esso
7. Clicca "OK"


RISOLUZIONE PROBLEMI
====================

Se l'add-in non appare:
- Controlla la sezione "Componenti aggiuntivi disabilitati"
- Se presente, selezionalo e clicca "Abilita"

Se l'add-in è disabilitato automaticamente:
- Potrebbe esserci un errore nel codice
- Controlla i log di Windows per errori specifici

Se nulla funziona:
- Assicurati che .NET Framework 4.7.2+ sia installato
- Assicurati che VSTO Runtime sia installato
- Prova a ricompilare l'add-in con Visual Studio


DISINSTALLAZIONE
================

Per rimuovere l'add-in:
1. Apri l'Editor del Registro
2. Elimina la chiave: HKEY_CURRENT_USER\Software\Microsoft\Office\Outlook\Addins\OutlookAddIn1.ThisAddIn
3. Opzionalmente, deregistra la DLL:
   "%ProgramFiles(x86)%\Microsoft.NET\Framework\v4.0.30319\RegAsm.exe" OutlookAddIn1.dll /unregister
