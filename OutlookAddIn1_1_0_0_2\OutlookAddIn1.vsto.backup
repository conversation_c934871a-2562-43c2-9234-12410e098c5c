﻿<?xml version="1.0" encoding="utf-8"?>
<asmv1:assembly xsi:schemaLocation="urn:schemas-microsoft-com:asm.v1 assembly.adaptive.xsd" manifestVersion="1.0" xmlns:asmv1="urn:schemas-microsoft-com:asm.v1" xmlns="urn:schemas-microsoft-com:asm.v2" xmlns:asmv2="urn:schemas-microsoft-com:asm.v2" xmlns:xrml="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:asmv3="urn:schemas-microsoft-com:asm.v3" xmlns:dsig="http://www.w3.org/2000/09/xmldsig#" xmlns:co.v1="urn:schemas-microsoft-com:clickonce.v1" xmlns:co.v2="urn:schemas-microsoft-com:clickonce.v2">
  <assemblyIdentity name="OutlookAddIn1.vsto" version="*******" publicKeyToken="58c2175c2f2f3508" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <description asmv2:publisher="OutlookAddIn1" asmv2:product="OutlookAddIn1" xmlns="urn:schemas-microsoft-com:asm.v1" />
  <deployment install="false" mapFileExtensions="true" />
  <compatibleFrameworks xmlns="urn:schemas-microsoft-com:clickonce.v2">
    <framework targetVersion="4.7.2" profile="Full" supportedRuntime="4.0.30319" />
  </compatibleFrameworks>
  <dependency>
    <dependentAssembly dependencyType="install" codebase="OutlookAddIn1_1_0_0_2\OutlookAddIn1.dll.manifest" size="13693">
      <assemblyIdentity name="OutlookAddIn1.dll" version="*******" publicKeyToken="58c2175c2f2f3508" language="neutral" processorArchitecture="msil" type="win32" />
      <hash>
        <dsig:Transforms>
          <dsig:Transform Algorithm="urn:schemas-microsoft-com:HashTransforms.Identity" />
        </dsig:Transforms>
        <dsig:DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" />
        <dsig:DigestValue>PSwYXL2VXiSpDyj8jEskhBT5Fa3Skcf0OqYqZR7s8VA=</dsig:DigestValue>
      </hash>
    </dependentAssembly>
  </dependency>
<publisherIdentity name="CN=PC-Corso9\Corso9" issuerKeyHash="aaa2e78cf74d8c563dc9b24936b0d4b13d90613f" /><Signature Id="StrongNameSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>OjMvnvAgUp5sujsyAwm/0iZr6DY8rJlpE+Tw5aseIxk=</DigestValue></Reference></SignedInfo><SignatureValue>Iom5DS1zt+9d+Qd9AzRfaVN0E+ISWbDMWzjwaVZfvLRGlv9ldY1NRx6IAVgAjSyNeT5VpdbG/F2Rka4x7kLiYd8BWptZE0UgW6aBcUp5yjW70r303rt1oenz4ece+6YcOcVHQB8qC163Z8ebA10cJC6uRfCgkoJeORlFxMJlLhI=</SignatureValue><KeyInfo Id="StrongNameKeyInfo"><KeyValue><RSAKeyValue><Modulus>yxphkvU8j4g9sYABhdtqu4su7hTBo0TYy0PSDutbRV5hps5QiBgaurut7aire//Vdd7U1bH6sA+LwnkHsgujg77FlLuaOdF93ZAI+9m5a2BpwDToc10UYeyIfvjjchaavwJLsP8cujh2k53RVhaNgQ3impeYkrlxiHjqkWA+s9s=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><msrel:RelData xmlns:msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"><r:license xmlns:r="urn:mpeg:mpeg21:2003:01-REL-R-NS" xmlns:as="http://schemas.microsoft.com/windows/pki/2005/Authenticode"><r:grant><as:ManifestInformation Hash="19231eabe5f0e4136999ac3c36e86b26d2bf0903323bba6c9e5220f09e2f333a" Description="" Url=""><as:assemblyIdentity name="OutlookAddIn1.vsto" version="*******" publicKeyToken="58c2175c2f2f3508" language="neutral" processorArchitecture="msil" xmlns="urn:schemas-microsoft-com:asm.v1" /></as:ManifestInformation><as:SignedBy /><as:AuthenticodePublisher><as:X509SubjectName>CN=PC-Corso9\Corso9</as:X509SubjectName></as:AuthenticodePublisher></r:grant><r:issuer><Signature Id="AuthenticodeSignature" xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha256" /><Reference URI=""><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature" /><Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#" /></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha256" /><DigestValue>kBJ4Jl2Sqn+bU9f3V3ToDb6R2EjA+d63Y4TwmNuylR4=</DigestValue></Reference></SignedInfo><SignatureValue>jtMwH4qxwzUzm24Yths41Cn3TVz9XhE1J4ZaficUpXewVUXcrir6+JjpeaZed8F3ieDkxYziYbdCd887FK+ZfnKcnEPOVwXyvUPRsKBOO2vCrSchfibOcQzUVKHQD0gRmTdzZ7UUJq0QCk4fmVLo459Orat95LhBp8GShyCnWkI=</SignatureValue><KeyInfo><KeyValue><RSAKeyValue><Modulus>yxphkvU8j4g9sYABhdtqu4su7hTBo0TYy0PSDutbRV5hps5QiBgaurut7aire//Vdd7U1bH6sA+LwnkHsgujg77FlLuaOdF93ZAI+9m5a2BpwDToc10UYeyIfvjjchaavwJLsP8cujh2k53RVhaNgQ3impeYkrlxiHjqkWA+s9s=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue></KeyValue><X509Data><X509Certificate>MIIB2TCCAUKgAwIBAgIQF54HHtx5BYBGn86calRA5zANBgkqhkiG9w0BAQsFADArMSkwJwYDVQQDHiAAUABDAC0AQwBvAHIAcwBvADkAXABDAG8AcgBzAG8AOTAeFw0xNzA3MTUwNzMzMzBaFw0xODA3MTUxMzMzMzBaMCsxKTAnBgNVBAMeIABQAEMALQBDAG8AcgBzAG8AOQBcAEMAbwByAHMAbwA5MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLGmGS9TyPiD2xgAGF22q7iy7uFMGjRNjLQ9IO61tFXmGmzlCIGBq6u63tqKt7/9V13tTVsfqwD4vCeQeyC6ODvsWUu5o50X3dkAj72blrYGnANOhzXRRh7Ih++ONyFpq/Akuw/xy6OHaTndFWFo2BDeKal5iSuXGIeOqRYD6z2wIDAQABMA0GCSqGSIb3DQEBCwUAA4GBAJ+UG3Yq0vBzpnZ9oNFmD+vM5J0QfDza+EPGyRmR2Do2vCtv/Lyqu3jKH+C1ePFI0bPtli7BL3gUwbqJG8w/7UDKbEEikgU7re9R4nq9R1asVJMIQJXTHLwBuNTsHOy0xUnoIJdHTfi8HneMee9SRXmQw9lDrQw3P7y2nDpVQMk4</X509Certificate></X509Data></KeyInfo></Signature></r:issuer></r:license></msrel:RelData></KeyInfo></Signature></asmv1:assembly>