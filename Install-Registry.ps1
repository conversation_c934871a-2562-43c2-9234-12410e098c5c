# Script per installare l'add-in direttamente nel registry (metodo più affidabile)
param(
    [string]$ManifestDir = ".\OutlookAddIn1_1_0_0_2"
)

Write-Host "=== Installazione Add-in Outlook (Registry) ===" -ForegroundColor Green

$vstoFile = Join-Path $ManifestDir "OutlookAddIn1.vsto"

# Verifica che il file esista
if (-not (Test-Path $vstoFile)) {
    Write-Host "ERRORE: File .vsto non trovato: $vstoFile" -ForegroundColor Red
    Read-Host "Premi Enter per uscire"
    exit 1
}

try {
    $FullPath = (Resolve-Path $vstoFile).Path
    Write-Host "Percorso add-in: $FullPath" -ForegroundColor Yellow
    
    # Registrazione nel registry di Outlook
    $regPath = "HKCU:\Software\Microsoft\Office\Outlook\Addins\OutlookAddIn1"
    
    Write-Host "Registrazione add-in nel registry..." -ForegroundColor Yellow
    
    # Rimuovi registrazione precedente se esiste
    if (Test-Path $regPath) {
        Remove-Item $regPath -Recurse -Force
        Write-Host "Registrazione precedente rimossa" -ForegroundColor Gray
    }
    
    # Crea nuova registrazione
    New-Item -Path $regPath -Force | Out-Null
    
    # Imposta le proprietà dell'add-in
    Set-ItemProperty -Path $regPath -Name "Description" -Value "OutlookAddIn1 - Add-in personalizzato"
    Set-ItemProperty -Path $regPath -Name "FriendlyName" -Value "OutlookAddIn1"
    Set-ItemProperty -Path $regPath -Name "LoadBehavior" -Value 3 -Type DWord
    Set-ItemProperty -Path $regPath -Name "Manifest" -Value $FullPath
    
    Write-Host "Add-in registrato con successo!" -ForegroundColor Green
    
    # Verifica la registrazione
    Write-Host ""
    Write-Host "Verifica registrazione:" -ForegroundColor Cyan
    $regValues = Get-ItemProperty -Path $regPath
    Write-Host "- Description: $($regValues.Description)" -ForegroundColor Gray
    Write-Host "- FriendlyName: $($regValues.FriendlyName)" -ForegroundColor Gray
    Write-Host "- LoadBehavior: $($regValues.LoadBehavior)" -ForegroundColor Gray
    Write-Host "- Manifest: $($regValues.Manifest)" -ForegroundColor Gray
    
    Write-Host ""
    Write-Host "=== INSTALLAZIONE COMPLETATA ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "PROSSIMI PASSI:" -ForegroundColor Yellow
    Write-Host "1. CHIUDI Outlook completamente se e' aperto"
    Write-Host "2. RIAVVIA Outlook"
    Write-Host "3. L'add-in dovrebbe caricarsi automaticamente"
    Write-Host ""
    Write-Host "VERIFICA:" -ForegroundColor Cyan
    Write-Host "- Vai in File > Opzioni > Componenti aggiuntivi"
    Write-Host "- Seleziona 'Componenti aggiuntivi COM' dal menu a discesa"
    Write-Host "- Clicca 'Vai...'"
    Write-Host "- Verifica che 'OutlookAddIn1' sia presente e abilitato"
    Write-Host ""
    Write-Host "Se l'add-in non appare o e' disabilitato:"
    Write-Host "- Controlla la sezione 'Componenti aggiuntivi disabilitati'"
    Write-Host "- Se presente, selezionalo e clicca 'Abilita'"
    
} catch {
    Write-Host ""
    Write-Host "ERRORE durante la registrazione: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Assicurati di eseguire lo script come Amministratore" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Premi Enter per uscire"
