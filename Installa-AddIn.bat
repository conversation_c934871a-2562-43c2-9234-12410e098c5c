@echo off
echo =================================
echo   Installazione Add-in Outlook
echo =================================
echo.

REM Verifica se il file PowerShell esiste
if not exist "Install-Fixed.ps1" (
    echo ERRORE: File Install-Fixed.ps1 non trovato!
    pause
    exit /b 1
)

REM Verifica se il file VSTO esiste
if not exist "OutlookAddIn1_1_0_0_2\OutlookAddIn1.vsto" (
    echo ERRORE: File OutlookAddIn1.vsto non trovato!
    pause
    exit /b 1
)

echo Correzione e installazione add-in...
echo NOTA: Questo script corregge automaticamente i problemi di firma digitale
echo.

REM Esegui lo script PowerShell
powershell -ExecutionPolicy Bypass -File "Install-Fixed.ps1"

echo.
echo Installazione completata.
echo Riavvia Outlook per vedere l'add-in.
echo.
pause
