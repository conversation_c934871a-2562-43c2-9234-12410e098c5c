@echo off
echo =================================
echo   Installazione Add-in Outlook
echo =================================
echo.

REM Verifica se il file PowerShell esiste
if not exist "Install-OutlookAddIn.ps1" (
    echo ERRORE: File Install-OutlookAddIn.ps1 non trovato!
    pause
    exit /b 1
)

REM Verifica se il file VSTO esiste
if not exist "OutlookAddIn1_1_0_0_2\OutlookAddIn1.vsto" (
    echo ERRORE: File OutlookAddIn1.vsto non trovato!
    pause
    exit /b 1
)

echo Esecuzione script PowerShell...
echo.

REM Esegui lo script PowerShell
powershell -ExecutionPolicy Bypass -File "Install-OutlookAddIn.ps1"

echo.
echo Installazione completata.
echo Riavvia Outlook per vedere l'add-in.
echo.
pause
