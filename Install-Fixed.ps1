# Script completo per correggere e installare l'add-in di Outlook
param(
    [string]$ManifestDir = ".\OutlookAddIn1_1_0_0_2"
)

Write-Host "=== Installazione Add-in Outlook (con correzione) ===" -ForegroundColor Green

$vstoFile = Join-Path $ManifestDir "OutlookAddIn1.vsto"
$manifestFile = Join-Path $ManifestDir "OutlookAddIn1.dll.manifest"

# Verifica che i file esistano
if (-not (Test-Path $vstoFile)) {
    Write-Host "ERRORE: File .vsto non trovato: $vstoFile" -ForegroundColor Red
    Read-Host "Premi Enter per uscire"
    exit 1
}

if (-not (Test-Path $manifestFile)) {
    Write-Host "ERRORE: File manifest non trovato: $manifestFile" -ForegroundColor Red
    Read-Host "Premi Enter per uscire"
    exit 1
}

try {
    # FASE 1: Correzione dei manifest
    Write-Host "FASE 1: Correzione manifest..." -ForegroundColor Yellow
    
    # Backup dei file originali
    Copy-Item $vstoFile "$vstoFile.backup" -Force
    Copy-Item $manifestFile "$manifestFile.backup" -Force
    Write-Host "Backup creati" -ForegroundColor Gray
    
    # Correggi il file .vsto
    $vstoContent = Get-Content $vstoFile -Raw
    
    # Rimuovi le firme digitali usando regex (più semplice)
    $vstoContent = $vstoContent -replace '<Signature[^>]*>.*?</Signature>', ''
    $vstoContent = $vstoContent -replace '<publisherIdentity[^>]*\s*/>', ''
    $vstoContent = $vstoContent -replace '<msrel:RelData[^>]*>.*?</msrel:RelData>', ''
    
    # Salva il file .vsto corretto
    $vstoContent | Out-File $vstoFile -Encoding UTF8
    Write-Host "File .vsto corretto" -ForegroundColor Green
    
    # Correggi il file manifest
    $manifestContent = Get-Content $manifestFile -Raw
    
    # Rimuovi le firme digitali e gli hash
    $manifestContent = $manifestContent -replace '<Signature[^>]*>.*?</Signature>', ''
    $manifestContent = $manifestContent -replace '<publisherIdentity[^>]*\s*/>', ''
    $manifestContent = $manifestContent -replace '<msrel:RelData[^>]*>.*?</msrel:RelData>', ''
    $manifestContent = $manifestContent -replace '<hash>.*?</hash>', ''
    
    # Salva il file manifest corretto
    $manifestContent | Out-File $manifestFile -Encoding UTF8
    Write-Host "File manifest corretto" -ForegroundColor Green
    
    # FASE 2: Installazione
    Write-Host ""
    Write-Host "FASE 2: Installazione add-in..." -ForegroundColor Yellow
    
    $FullPath = (Resolve-Path $vstoFile).Path
    
    # Registrazione diretta nel registry (metodo più affidabile)
    $regPath = "HKCU:\Software\Microsoft\Office\Outlook\Addins\OutlookAddIn1"
    
    if (Test-Path $regPath) {
        Remove-Item $regPath -Recurse -Force
        Write-Host "Registrazione precedente rimossa" -ForegroundColor Gray
    }
    
    New-Item -Path $regPath -Force | Out-Null
    Set-ItemProperty -Path $regPath -Name "Description" -Value "OutlookAddIn1"
    Set-ItemProperty -Path $regPath -Name "FriendlyName" -Value "OutlookAddIn1"
    Set-ItemProperty -Path $regPath -Name "LoadBehavior" -Value 3 -Type DWord
    Set-ItemProperty -Path $regPath -Name "Manifest" -Value $FullPath
    
    Write-Host "Add-in registrato nel registry!" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== INSTALLAZIONE COMPLETATA ===" -ForegroundColor Green
    Write-Host "1. Chiudi Outlook completamente se e' aperto"
    Write-Host "2. Riavvia Outlook"
    Write-Host "3. L'add-in dovrebbe apparire automaticamente"
    Write-Host ""
    Write-Host "Per verificare:"
    Write-Host "- Vai in File > Opzioni > Componenti aggiuntivi"
    Write-Host "- Seleziona 'Componenti aggiuntivi COM' e clicca 'Vai'"
    Write-Host "- Verifica che 'OutlookAddIn1' sia presente e abilitato"
    
} catch {
    Write-Host ""
    Write-Host "ERRORE: $($_.Exception.Message)" -ForegroundColor Red
    
    # Ripristina i backup in caso di errore
    if (Test-Path "$vstoFile.backup") {
        Copy-Item "$vstoFile.backup" $vstoFile -Force
        Write-Host "File .vsto ripristinato dal backup" -ForegroundColor Yellow
    }
    if (Test-Path "$manifestFile.backup") {
        Copy-Item "$manifestFile.backup" $manifestFile -Force
        Write-Host "File manifest ripristinato dal backup" -ForegroundColor Yellow
    }
}

Write-Host ""
Read-Host "Premi Enter per uscire"
