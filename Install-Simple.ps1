# Script semplificato per installare l'add-in di Outlook
param(
    [string]$AddInPath = ".\OutlookAddIn1_1_0_0_2\OutlookAddIn1.vsto"
)

Write-Host "=== Installazione Add-in Outlook ===" -ForegroundColor Green

# Verifica se il file esiste
if (-not (Test-Path $AddInPath)) {
    Write-Host "ERRORE: File add-in non trovato: $AddInPath" -ForegroundColor Red
    Read-Host "Premi Enter per uscire"
    exit 1
}

# Ottieni il percorso completo
$FullPath = (Resolve-Path $AddInPath).Path
Write-Host "Percorso add-in: $FullPath" -ForegroundColor Yellow

try {
    # Prova prima con VSTOInstaller
    $vstoInstaller = "${env:ProgramFiles}\Common Files\Microsoft Shared\VSTO\10.0\VSTOInstaller.exe"
    if (-not (Test-Path $vstoInstaller)) {
        $vstoInstaller = "${env:ProgramFiles(x86)}\Common Files\Microsoft Shared\VSTO\10.0\VSTOInstaller.exe"
    }
    
    if (Test-Path $vstoInstaller) {
        Write-Host "Installazione con VSTOInstaller..." -ForegroundColor Yellow
        Start-Process -FilePath $vstoInstaller -ArgumentList "/Install", "`"$FullPath`"" -Wait
        Write-Host "Installazione completata!" -ForegroundColor Green
    } else {
        # Registrazione manuale nel registry
        Write-Host "VSTOInstaller non trovato, registrazione manuale..." -ForegroundColor Yellow
        
        $regPath = "HKCU:\Software\Microsoft\Office\Outlook\Addins\OutlookAddIn1"
        
        if (-not (Test-Path $regPath)) {
            New-Item -Path $regPath -Force | Out-Null
        }
        
        Set-ItemProperty -Path $regPath -Name "Description" -Value "OutlookAddIn1"
        Set-ItemProperty -Path $regPath -Name "FriendlyName" -Value "OutlookAddIn1"
        Set-ItemProperty -Path $regPath -Name "LoadBehavior" -Value 3 -Type DWord
        Set-ItemProperty -Path $regPath -Name "Manifest" -Value $FullPath
        
        Write-Host "Add-in registrato nel registry!" -ForegroundColor Green
    }
    
    Write-Host ""
    Write-Host "=== IMPORTANTE ===" -ForegroundColor Cyan
    Write-Host "1. Riavvia Outlook completamente"
    Write-Host "2. Vai in File > Opzioni > Componenti aggiuntivi"
    Write-Host "3. Verifica che OutlookAddIn1 sia presente e abilitato"
    
} catch {
    Write-Host "ERRORE: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Prova a eseguire lo script come Amministratore" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Premi Enter per uscire"
