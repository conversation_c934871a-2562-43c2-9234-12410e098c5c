# Installazione Add-in Outlook

## Problemi Risolti

Ho identificato e corretto i seguenti problemi che impedivano l'installazione dell'add-in:

### 1. **Percorso Codebase Errato**
- **Problema**: Il file `.vsto` faceva riferimento a `Application Files\OutlookAddIn1_1_0_0_2\` invece del percorso corretto
- **Soluzione**: Corretto il percorso in `OutlookAddIn1_1_0_0_2\`

### 2. **Inconsistenza Versioni**
- **Problema**: Versioni diverse tra file `.vsto` (*******) e manifest (*******)
- **Soluzione**: Uniformato tutte le versioni a `*******`

### 3. **Framework .NET Obsoleto**
- **Problema**: L'add-in usava .NET Framework 4.5.2, non ottimale per Outlook 365
- **Soluzione**: Aggiornato a .NET Framework 4.7.2 per migliore compatibilità

### 4. **Firma Digitale Invalida**
- **Problema**: Le modifiche ai manifest hanno invalidato le firme digitali
- **Soluzione**: Creato script automatico per rimuovere le firme e permettere l'installazione

## Prerequisiti

Prima di installare l'add-in, assicurati di avere:

1. **Microsoft Office/Outlook 365** installato
2. **.NET Framework 4.7.2 o superiore**
   - Scarica da: https://dotnet.microsoft.com/download/dotnet-framework
3. **Visual Studio Tools for Office Runtime**
   - Scarica da: https://www.microsoft.com/download/details.aspx?id=48217

## Metodi di Installazione

### Metodo 1: Script Automatico (Raccomandato)

1. **Esegui come Amministratore** il file `Installa-AddIn.bat`
2. Lo script correggerà automaticamente i problemi di firma digitale
3. Registrerà l'add-in nel registry di Windows
4. **IMPORTANTE**: Chiudi e riavvia Outlook dopo l'installazione

### Metodo 2: Installazione Manuale

1. **Chiudi Outlook** completamente
2. **Fai doppio clic** sul file `OutlookAddIn1_1_0_0_2\OutlookAddIn1.vsto`
3. Segui le istruzioni della procedura guidata di installazione
4. **Riavvia Outlook**

### Metodo 3: Registrazione Registry (Se gli altri falliscono)

1. Apri **Editor del Registro** (regedit) come Amministratore
2. Vai a: `HKEY_CURRENT_USER\Software\Microsoft\Office\Outlook\Addins`
3. Crea una nuova chiave chiamata `OutlookAddIn1`
4. Aggiungi i seguenti valori:
   - `Description` (Stringa): "OutlookAddIn1"
   - `FriendlyName` (Stringa): "OutlookAddIn1"
   - `LoadBehavior` (DWORD): 3
   - `Manifest` (Stringa): Percorso completo al file `.vsto`

## Verifica Installazione

1. Apri **Outlook**
2. Vai in **File > Opzioni > Componenti aggiuntivi**
3. Seleziona **"Componenti aggiuntivi COM"** dal menu a discesa
4. Clicca **"Vai..."**
5. Verifica che **"OutlookAddIn1"** sia presente e abilitato

## Risoluzione Problemi

### L'add-in non appare in Outlook

1. **Verifica i prerequisiti** (vedi sopra)
2. **Controlla i componenti aggiuntivi disabilitati**:
   - File > Opzioni > Componenti aggiuntivi
   - Seleziona "Componenti aggiuntivi disabilitati"
   - Se presente, riabilitalo
3. **Reinstalla l'add-in** usando il Metodo 3 (Registry)

### Errore "Impossibile installare"

1. **Esegui come Amministratore**
2. **Disabilita temporaneamente l'antivirus**
3. **Verifica che tutti i file siano presenti** nella cartella `OutlookAddIn1_1_0_0_2`

### Errore di certificato

1. Il certificato è auto-firmato e potrebbe essere considerato non attendibile
2. **Opzioni**:
   - Accetta il rischio durante l'installazione
   - Oppure firma l'add-in con un certificato valido

### Outlook 365 Online vs Desktop

- Questo add-in è compatibile solo con **Outlook Desktop**
- Non funziona con **Outlook Web** (outlook.com)

## File Modificati

I seguenti file sono stati corretti:

- `OutlookAddIn1_1_0_0_2/OutlookAddIn1.vsto`
- `OutlookAddIn1_1_0_0_2/OutlookAddIn1.dll.manifest`

## Supporto

Se continui ad avere problemi:

1. Verifica che tutti i prerequisiti siano installati
2. Prova l'installazione manuale (Metodo 2)
3. Controlla i log di Windows per errori specifici
4. Considera di ricompilare l'add-in con Visual Studio per un certificato aggiornato
