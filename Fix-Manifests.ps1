# Script per rimuovere le firme digitali dai manifest e correggere i problemi
param(
    [string]$ManifestDir = ".\OutlookAddIn1_1_0_0_2"
)

Write-Host "=== Correzione Manifest Add-in Outlook ===" -ForegroundColor Green

$vstoFile = Join-Path $ManifestDir "OutlookAddIn1.vsto"
$manifestFile = Join-Path $ManifestDir "OutlookAddIn1.dll.manifest"

if (-not (Test-Path $vstoFile)) {
    Write-Host "ERRORE: File .vsto non trovato: $vstoFile" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $manifestFile)) {
    Write-Host "ERRORE: File manifest non trovato: $manifestFile" -ForegroundColor Red
    exit 1
}

try {
    Write-Host "Rimozione firme digitali..." -ForegroundColor Yellow
    
    # Leggi il contenuto del file .vsto
    [xml]$vstoXml = Get-Content $vstoFile -Encoding UTF8
    
    # Rimuovi tutti i nodi Signature
    $signatures = $vstoXml.SelectNodes("//ds:Signature", @{ds="http://www.w3.org/2000/09/xmldsig#"})
    foreach ($sig in $signatures) {
        $sig.ParentNode.RemoveChild($sig) | Out-Null
    }
    
    # Rimuovi publisherIdentity
    $pubId = $vstoXml.SelectSingleNode("//publisherIdentity")
    if ($pubId) {
        $pubId.ParentNode.RemoveChild($pubId) | Out-Null
    }
    
    # Rimuovi msrel:RelData
    $relData = $vstoXml.SelectNodes("//msrel:RelData", @{msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"})
    foreach ($rel in $relData) {
        $rel.ParentNode.RemoveChild($rel) | Out-Null
    }
    
    # Salva il file .vsto modificato
    $vstoXml.Save($vstoFile)
    Write-Host "File .vsto corretto" -ForegroundColor Green
    
    # Leggi il contenuto del file manifest
    [xml]$manifestXml = Get-Content $manifestFile -Encoding UTF8
    
    # Rimuovi tutti i nodi Signature dal manifest
    $signatures = $manifestXml.SelectNodes("//ds:Signature", @{ds="http://www.w3.org/2000/09/xmldsig#"})
    foreach ($sig in $signatures) {
        $sig.ParentNode.RemoveChild($sig) | Out-Null
    }
    
    # Rimuovi publisherIdentity dal manifest
    $pubId = $manifestXml.SelectSingleNode("//publisherIdentity")
    if ($pubId) {
        $pubId.ParentNode.RemoveChild($pubId) | Out-Null
    }
    
    # Rimuovi msrel:RelData dal manifest
    $relData = $manifestXml.SelectNodes("//msrel:RelData", @{msrel="http://schemas.microsoft.com/windows/rel/2005/reldata"})
    foreach ($rel in $relData) {
        $rel.ParentNode.RemoveChild($rel) | Out-Null
    }
    
    # Rimuovi tutti gli hash dai dependency
    $hashes = $manifestXml.SelectNodes("//hash")
    foreach ($hash in $hashes) {
        $hash.ParentNode.RemoveChild($hash) | Out-Null
    }
    
    # Salva il file manifest modificato
    $manifestXml.Save($manifestFile)
    Write-Host "File manifest corretto" -ForegroundColor Green
    
    Write-Host ""
    Write-Host "=== Correzione completata ===" -ForegroundColor Green
    Write-Host "I manifest sono stati corretti rimuovendo le firme digitali invalide."
    Write-Host "Ora puoi provare a installare l'add-in."
    
} catch {
    Write-Host "ERRORE durante la correzione: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Dettagli: $($_.Exception.ToString())" -ForegroundColor Red
}

Write-Host ""
Read-Host "Premi Enter per continuare"
